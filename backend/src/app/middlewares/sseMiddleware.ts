import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import rateLimit from 'express-rate-limit';
import config from '../config';
import { Logger } from '../config/logger';
import AppError from '../errors/AppError';
import httpStatus from 'http-status';
import { JwtUserPayload } from '../interface/auth';

export interface AuthenticatedSSERequest extends Request {
  user?: JwtUserPayload;
}

/**
 * Authentication middleware for SSE connections
 */
export const authenticateSSE = async (
  req: AuthenticatedSSERequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from query parameter or Authorization header
    let token = req.query.token as string;
    
    if (!token) {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    if (!token) {
      throw new AppError(httpStatus.UNAUTHORIZED, 'Access token is required for SSE connection');
    }

    // Verify JWT token
    const decoded = jwt.verify(token, config.jwt_access_secret as string) as JwtUserPayload;
    
    if (!decoded || !decoded._id) {
      throw new AppError(httpStatus.UNAUTHORIZED, 'Invalid access token');
    }

    // Set user information using JwtUserPayload structure
    req.user = {
      _id: decoded._id,
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      iat: decoded.iat,
      exp: decoded.exp
    };

    Logger.info(`SSE authentication successful for user: ${req.user._id} (${req.user.role})`);
    next();
  } catch (error) {
    Logger.error('SSE authentication failed:', error);
    
    // Send error as SSE event and close connection
    res.writeHead(401, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    });
    
    const errorMessage = {
      id: `error_${Date.now()}`,
      type: 'authentication_error',
      data: {
        error: 'Authentication failed',
        message: error instanceof AppError ? error.message : 'Invalid token'
      },
      timestamp: new Date()
    };
    
    res.write(`event: error\ndata: ${JSON.stringify(errorMessage)}\n\n`);
    res.end();
  }
};

/**
 * Rate limiting middleware for SSE connections
 */
export const sseRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 SSE connections per windowMs
  message: {
    error: 'Too many SSE connection attempts',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    Logger.warn(`SSE rate limit exceeded for IP: ${req.ip}`);
    
    res.writeHead(429, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Retry-After': '900' // 15 minutes in seconds
    });
    
    const errorMessage = {
      id: `rate_limit_${Date.now()}`,
      type: 'rate_limit_error',
      data: {
        error: 'Rate limit exceeded',
        message: 'Too many connection attempts. Please try again later.',
        retryAfter: 900
      },
      timestamp: new Date()
    };
    
    res.write(`event: error\ndata: ${JSON.stringify(errorMessage)}\n\n`);
    res.end();
  }
});

/**
 * CORS middleware for SSE connections
 */
export const sseCORS = (req: Request, res: Response, next: NextFunction): void => {
  const allowedOrigins = process.env.NODE_ENV === 'production'
    ? [
        'https://green-uni-mind-di79.vercel.app',
        'https://green-uni-mind.pages.dev',
        'https://green-uni-mind-backend-oxpo.onrender.com'
      ]
    : [
        'http://localhost:3000',
        'http://localhost:5173',
        'http://localhost:8080',
        'http://localhost:8081',
        'http://localhost:5000'
      ];

  const origin = req.headers.origin;
  
  if (!origin || allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin || '*');
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Authorization, Cache-Control');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }
  
  next();
};

/**
 * Connection validation middleware
 */
export const validateSSEConnection = (
  req: AuthenticatedSSERequest,
  res: Response,
  next: NextFunction
): void => {
  // Check if client supports SSE
  const acceptHeader = req.headers.accept;
  if (!acceptHeader || !acceptHeader.includes('text/event-stream')) {
    Logger.warn(`Client does not support SSE: ${req.ip}`);
    
    res.status(400).json({
      error: 'SSE not supported',
      message: 'Client must support Server-Sent Events (text/event-stream)'
    });
    return;
  }

  // Validate user type
  if (!req.user || !['user', 'teacher', 'student'].includes(req.user.role)) {
    Logger.warn(`Invalid user role for SSE connection: ${req.user?.role}`);
    
    res.status(400).json({
      error: 'Invalid user role',
      message: 'User role must be user, teacher, or student'
    });
    return;
  }

  next();
};

/**
 * Connection logging middleware
 */
export const logSSEConnection = (
  req: AuthenticatedSSERequest,
  res: Response,
  next: NextFunction
): void => {
  const startTime = Date.now();
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const ip = req.ip || req.connection.remoteAddress || 'Unknown';
  
  Logger.info(`SSE connection attempt from ${ip} - User: ${req.user?._id} (${req.user?.role}) - UA: ${userAgent}`);
  
  // Log connection duration when it ends
  res.on('close', () => {
    const duration = Date.now() - startTime;
    Logger.info(`SSE connection closed - Duration: ${duration}ms - User: ${req.user?._id}`);
  });
  
  res.on('error', (error) => {
    const duration = Date.now() - startTime;
    Logger.error(`SSE connection error - Duration: ${duration}ms - User: ${req.user?._id} - Error:`, error);
  });
  
  next();
};

/**
 * Health check middleware for SSE endpoint
 */
export const sseHealthCheck = (req: Request, res: Response, next: NextFunction): void => {
  // Simple health check for SSE endpoint
  if (req.path === '/health' || req.path === '/ping') {
    res.json({
      status: 'ok',
      service: 'SSE',
      timestamp: new Date(),
      uptime: process.uptime()
    });
    return;
  }
  
  next();
};

/**
 * Error handling middleware for SSE
 */
export const sseErrorHandler = (
  error: any,
  req: AuthenticatedSSERequest,
  res: Response,
  next: NextFunction
): void => {
  Logger.error('SSE middleware error:', error);
  
  if (res.headersSent) {
    // If headers are already sent, we can't send a proper HTTP error
    // Just close the connection
    res.end();
    return;
  }
  
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';
  
  res.writeHead(statusCode, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive'
  });
  
  const errorMessage = {
    id: `error_${Date.now()}`,
    type: 'server_error',
    data: {
      error: 'Server error',
      message: message,
      statusCode: statusCode
    },
    timestamp: new Date()
  };
  
  res.write(`event: error\ndata: ${JSON.stringify(errorMessage)}\n\n`);
  res.end();
};

/**
 * Combined SSE middleware stack
 */
export const sseMiddlewareStack = [
  sseCORS,
  sseHealthCheck,
  sseRateLimit,
  authenticateSSE,
  validateSSEConnection,
  logSSEConnection
];

export default {
  authenticateSSE,
  sseRateLimit,
  sseCORS,
  validateSSEConnection,
  logSSEConnection,
  sseHealthCheck,
  sseErrorHandler,
  sseMiddlewareStack
};
