import { Router } from 'express';
import { sseMiddlewareStack, sseError<PERSON><PERSON>ler, AuthenticatedSSERequest } from '../middlewares/sseMiddleware';
import { serviceRegistry } from '../services/ServiceRegistry';
import { Logger } from '../config/logger';

const router = Router();

// Helper function to get SSE service (lazy loading)
const getSSEService = () => serviceRegistry.getSSEService();

/**
 * Main SSE connection endpoint
 * GET /api/sse/connect
 */
router.get('/connect', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const clientId = getSSEService().createConnection(
      req,
      res,
      req.user._id,
      req.user.role
    );

    Logger.info(`SSE connection established: ${clientId} for user ${req.user._id}`);
  } catch (error) {
    Logger.error('Failed to establish SSE connection:', error);
    sseErrorHandler(error, req, res, () => {});
  }
});

/**
 * Join room endpoint
 * POST /api/sse/join-room
 */
router.post('/join-room', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { clientId, roomName } = req.body;
    
    if (!clientId || !roomName) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'clientId and roomName are required'
      });
    }

    const success = getSSEService().joinRoom(clientId, roomName);
    
    if (success) {
      res.json({
        success: true,
        message: `Joined room: ${roomName}`,
        clientId,
        roomName
      });
    } else {
      res.status(404).json({
        error: 'Client not found',
        message: 'Invalid client ID or client not connected'
      });
    }
  } catch (error) {
    Logger.error('❌ Failed to join room:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to join room'
    });
  }
});

/**
 * Leave room endpoint
 * POST /api/sse/leave-room
 */
router.post('/leave-room', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { clientId, roomName } = req.body;
    
    if (!clientId || !roomName) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'clientId and roomName are required'
      });
    }

    const success = getSSEService().leaveRoom(clientId, roomName);
    
    if (success) {
      res.json({
        success: true,
        message: `Left room: ${roomName}`,
        clientId,
        roomName
      });
    } else {
      res.status(404).json({
        error: 'Client not found',
        message: 'Invalid client ID or client not connected'
      });
    }
  } catch (error) {
    Logger.error('❌ Failed to leave room:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to leave room'
    });
  }
});

/**
 * Send message to user endpoint
 * POST /api/sse/send-to-user
 */
router.post('/send-to-user', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { targetUserId, type, data, priority = 'medium' } = req.body;
    
    if (!targetUserId || !type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'targetUserId, type, and data are required'
      });
    }

    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      targetUserId,
      priority,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };

    const sentCount = getSSEService().sendToUser(targetUserId, message);
    
    res.json({
      success: true,
      message: 'Message sent successfully',
      sentToConnections: sentCount,
      messageId: message.id
    });
  } catch (error) {
    Logger.error('❌ Failed to send message to user:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to send message'
    });
  }
});

/**
 * Send message to room endpoint
 * POST /api/sse/send-to-room
 */
router.post('/send-to-room', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { roomName, type, data, priority = 'medium' } = req.body;
    
    if (!roomName || !type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'roomName, type, and data are required'
      });
    }

    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      room: roomName,
      priority,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };

    const sentCount = getSSEService().sendToRoom(roomName, message);
    
    res.json({
      success: true,
      message: 'Message sent to room successfully',
      sentToConnections: sentCount,
      messageId: message.id,
      roomName
    });
  } catch (error) {
    Logger.error('❌ Failed to send message to room:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to send message to room'
    });
  }
});

/**
 * Broadcast message endpoint
 * POST /api/sse/broadcast
 */
router.post('/broadcast', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { type, data, priority = 'medium', userType } = req.body;
    
    if (!type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'type and data are required'
      });
    }

    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      priority,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };

    let sentCount: number;
    
    if (userType && ['student', 'teacher', 'admin'].includes(userType)) {
      sentCount = getSSEService().sendToUserType(userType, message);
    } else {
      sentCount = getSSEService().broadcast(message);
    }
    
    res.json({
      success: true,
      message: userType ? `Message broadcast to ${userType}s successfully` : 'Message broadcast successfully',
      sentToConnections: sentCount,
      messageId: message.id,
      userType: userType || 'all'
    });
  } catch (error) {
    Logger.error('❌ Failed to broadcast message:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to broadcast message'
    });
  }
});

/**
 * Get connection statistics endpoint
 * GET /api/sse/stats
 */
router.get('/stats', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const stats = getSSEService().getConnectionStats();
    const connectedUsers = getSSEService().getConnectedUsers();
    
    res.json({
      success: true,
      data: {
        connectionStats: stats,
        connectedUsers: connectedUsers,
        timestamp: new Date()
      }
    });
  } catch (error) {
    Logger.error('❌ Failed to get SSE stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve statistics'
    });
  }
});

/**
 * Health check endpoint
 * GET /api/sse/health
 */
router.get('/health', (req, res) => {
  try {
    const stats = getSSEService().getConnectionStats();
    
    res.json({
      status: 'healthy',
      service: 'SSE Service',
      timestamp: new Date(),
      uptime: process.uptime(),
      connections: {
        total: stats.totalConnections,
        active: stats.activeConnections
      }
    });
  } catch (error) {
    Logger.error('❌ SSE health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      service: 'SSE Service',
      error: 'Health check failed'
    });
  }
});

export default router;
