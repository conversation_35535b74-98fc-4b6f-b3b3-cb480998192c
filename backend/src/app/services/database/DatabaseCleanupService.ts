import mongoose from 'mongoose';
import { Logger } from '../../config/logger';

// Import models for cleanup (excluding preserved ones)
import { Course } from '../../modules/Course/course.model';
import { Student } from '../../modules/Student/student.model';
import { Payment } from '../../modules/Payment/payment.model';
import { Transaction } from '../../modules/Payment/transaction.model';
import { Lecture } from '../../modules/Lecture/lecture.model';
import { Bookmark } from '../../modules/Bookmark/bookmark.model';
import { Question } from '../../modules/Question/question.model';
import { Note } from '../../modules/Note/note.model';
import { 
  CourseAnalytics, 
  StudentEngagement, 
  RevenueAnalytics, 
  PerformanceMetrics, 
  AnalyticsSummary, 
  Activity 
} from '../../modules/Analytics/analytics.model';
import { 
  Message, 
  Conversation, 
  MessageThread, 
  MessageNotification, 
  MessageSearchIndex 
} from '../../modules/Messaging/messaging.model';

// Import preserved models for reference
import { User } from '../../modules/User/user.model';
import { Teacher } from '../../modules/Teacher/teacher.model';
import { Category } from '../../modules/Category/category.model';
import { SubCategory } from '../../modules/SubCategory/subCategory.model';

export interface CleanupOptions {
  preserveUsers: boolean;
  preserveTeachers: boolean;
  preserveCategories: boolean;
  preserveSubCategories: boolean;
  dryRun: boolean;
  batchSize: number;
}

export interface CleanupResult {
  success: boolean;
  preservedCounts: {
    users: number;
    teachers: number;
    categories: number;
    subCategories: number;
  };
  cleanedCounts: {
    courses: number;
    students: number;
    payments: number;
    transactions: number;
    lectures: number;
    bookmarks: number;
    questions: number;
    notes: number;
    analytics: number;
    messages: number;
    conversations: number;
  };
  errors: string[];
  duration: number;
}

export class DatabaseCleanupService {
  private readonly defaultOptions: CleanupOptions = {
    preserveUsers: true,
    preserveTeachers: true,
    preserveCategories: true,
    preserveSubCategories: true,
    dryRun: false,
    batchSize: 1000
  };

  /**
   * Perform selective database cleanup
   */
  async performCleanup(options: Partial<CleanupOptions> = {}): Promise<CleanupResult> {
    const startTime = Date.now();
    const opts = { ...this.defaultOptions, ...options };
    
    Logger.info('🧹 Starting database cleanup...', { options: opts });

    const result: CleanupResult = {
      success: false,
      preservedCounts: { users: 0, teachers: 0, categories: 0, subCategories: 0 },
      cleanedCounts: {
        courses: 0, students: 0, payments: 0, transactions: 0,
        lectures: 0, bookmarks: 0, questions: 0, notes: 0,
        analytics: 0, messages: 0, conversations: 0
      },
      errors: [],
      duration: 0
    };

    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        // Count preserved data
        result.preservedCounts = await this.countPreservedData();
        
        // Clean up models in dependency order
        await this.cleanupInOrder(result, opts, session);
        
        if (opts.dryRun) {
          Logger.info('🔍 Dry run completed - no actual changes made');
          await session.abortTransaction();
        } else {
          Logger.info('✅ Database cleanup transaction committed');
        }
      });

      result.success = true;
      result.duration = Date.now() - startTime;
      
      Logger.info('🎉 Database cleanup completed successfully', {
        duration: result.duration,
        preservedCounts: result.preservedCounts,
        cleanedCounts: result.cleanedCounts
      });

    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
      result.duration = Date.now() - startTime;
      
      Logger.error('❌ Database cleanup failed:', error);
      throw error;
    } finally {
      await session.endSession();
    }

    return result;
  }

  /**
   * Count preserved data before cleanup
   */
  private async countPreservedData(): Promise<CleanupResult['preservedCounts']> {
    const [users, teachers, categories, subCategories] = await Promise.all([
      User.countDocuments({ isDeleted: { $ne: true } }),
      Teacher.countDocuments({ isDeleted: { $ne: true } }),
      Category.countDocuments({ isActive: true }),
      SubCategory.countDocuments({ isActive: true })
    ]);

    return { users, teachers, categories, subCategories };
  }

  /**
   * Clean up models in proper dependency order
   */
  private async cleanupInOrder(
    result: CleanupResult,
    options: CleanupOptions,
    session: mongoose.ClientSession
  ): Promise<void> {
    // Clean up in reverse dependency order to avoid foreign key issues
    
    // 1. Analytics and activity data (no dependencies)
    result.cleanedCounts.analytics += await this.cleanupAnalytics(options, session);
    
    // 2. Messaging data (depends on users but we're preserving users)
    const messagingCounts = await this.cleanupMessaging(options, session);
    result.cleanedCounts.messages += messagingCounts.messages;
    result.cleanedCounts.conversations += messagingCounts.conversations;
    
    // 3. User-generated content (depends on courses and students)
    result.cleanedCounts.bookmarks += await this.cleanupBookmarks(options, session);
    result.cleanedCounts.questions += await this.cleanupQuestions(options, session);
    result.cleanedCounts.notes += await this.cleanupNotes(options, session);
    
    // 4. Payment and transaction data (depends on courses, students, teachers)
    result.cleanedCounts.payments += await this.cleanupPayments(options, session);
    result.cleanedCounts.transactions += await this.cleanupTransactions(options, session);
    
    // 5. Lectures (depends on courses)
    result.cleanedCounts.lectures += await this.cleanupLectures(options, session);
    
    // 6. Courses (depends on categories, teachers)
    result.cleanedCounts.courses += await this.cleanupCourses(options, session);
    
    // 7. Students (depends on users, but we need to clean student-specific data)
    result.cleanedCounts.students += await this.cleanupStudents(options, session);
  }

  /**
   * Clean up analytics data
   */
  private async cleanupAnalytics(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    let totalCleaned = 0;
    
    const models = [
      CourseAnalytics, StudentEngagement, RevenueAnalytics, 
      PerformanceMetrics, AnalyticsSummary, Activity
    ];

    for (const Model of models) {
      if (!options.dryRun) {
        const result = await Model.deleteMany({}).session(session);
        totalCleaned += result.deletedCount || 0;
      } else {
        const count = await Model.countDocuments({});
        totalCleaned += count;
      }
    }

    Logger.info(`🧹 Analytics cleanup: ${totalCleaned} records ${options.dryRun ? 'would be' : ''} removed`);
    return totalCleaned;
  }

  /**
   * Clean up messaging data
   */
  private async cleanupMessaging(
    options: CleanupOptions, 
    session: mongoose.ClientSession
  ): Promise<{ messages: number; conversations: number }> {
    let messages = 0;
    let conversations = 0;

    // Clean up messages first
    const messageModels = [Message, MessageThread, MessageNotification, MessageSearchIndex];
    for (const Model of messageModels) {
      if (!options.dryRun) {
        const result = await Model.deleteMany({}).session(session);
        messages += result.deletedCount || 0;
      } else {
        const count = await Model.countDocuments({});
        messages += count;
      }
    }

    // Clean up conversations
    if (!options.dryRun) {
      const result = await Conversation.deleteMany({}).session(session);
      conversations = result.deletedCount || 0;
    } else {
      conversations = await Conversation.countDocuments({});
    }

    Logger.info(`🧹 Messaging cleanup: ${messages} messages, ${conversations} conversations ${options.dryRun ? 'would be' : ''} removed`);
    return { messages, conversations };
  }

  /**
   * Clean up user-generated content
   */
  private async cleanupBookmarks(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Bookmark.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Bookmarks cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Bookmark.countDocuments({});
      Logger.info(`🧹 Bookmarks cleanup: ${count} records would be removed`);
      return count;
    }
  }

  private async cleanupQuestions(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Question.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Questions cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Question.countDocuments({});
      Logger.info(`🧹 Questions cleanup: ${count} records would be removed`);
      return count;
    }
  }

  private async cleanupNotes(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Note.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Notes cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Note.countDocuments({});
      Logger.info(`🧹 Notes cleanup: ${count} records would be removed`);
      return count;
    }
  }

  /**
   * Clean up payment data
   */
  private async cleanupPayments(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Payment.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Payments cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Payment.countDocuments({});
      Logger.info(`🧹 Payments cleanup: ${count} records would be removed`);
      return count;
    }
  }

  private async cleanupTransactions(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Transaction.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Transactions cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Transaction.countDocuments({});
      Logger.info(`🧹 Transactions cleanup: ${count} records would be removed`);
      return count;
    }
  }

  /**
   * Clean up lectures
   */
  private async cleanupLectures(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Lecture.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Lectures cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Lecture.countDocuments({});
      Logger.info(`🧹 Lectures cleanup: ${count} records would be removed`);
      return count;
    }
  }

  /**
   * Clean up courses
   */
  private async cleanupCourses(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Course.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Courses cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Course.countDocuments({});
      Logger.info(`🧹 Courses cleanup: ${count} records would be removed`);
      return count;
    }
  }

  /**
   * Clean up students (but preserve user accounts)
   */
  private async cleanupStudents(options: CleanupOptions, session: mongoose.ClientSession): Promise<number> {
    if (!options.dryRun) {
      const result = await Student.deleteMany({}).session(session);
      const count = result.deletedCount || 0;
      Logger.info(`🧹 Students cleanup: ${count} records removed`);
      return count;
    } else {
      const count = await Student.countDocuments({});
      Logger.info(`🧹 Students cleanup: ${count} records would be removed`);
      return count;
    }
  }

  /**
   * Verify data integrity after cleanup
   */
  async verifyIntegrity(): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = [];

    try {
      // Check that preserved data still exists
      const preservedCounts = await this.countPreservedData();
      
      if (preservedCounts.users === 0) {
        issues.push('No users found after cleanup');
      }
      
      if (preservedCounts.categories === 0) {
        issues.push('No categories found after cleanup');
      }

      // Check for orphaned references (this would indicate cleanup issues)
      const orphanedChecks = await Promise.all([
        Course.countDocuments({ categoryId: { $exists: true } }),
        Course.countDocuments({ creator: { $exists: true } })
      ]);

      if (orphanedChecks[0] > 0) {
        issues.push(`Found ${orphanedChecks[0]} courses with category references (should be 0 after cleanup)`);
      }

      Logger.info('🔍 Data integrity verification completed', {
        preservedCounts,
        issuesFound: issues.length
      });

      return {
        valid: issues.length === 0,
        issues
      };

    } catch (error) {
      issues.push(`Integrity check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { valid: false, issues };
    }
  }
}

// Export singleton instance
export const databaseCleanupService = new DatabaseCleanupService();
