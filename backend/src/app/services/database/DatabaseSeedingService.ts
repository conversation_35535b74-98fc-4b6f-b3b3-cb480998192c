import mongoose from 'mongoose';
import { Logger } from '../../config/logger';
import { Category } from '../../modules/Category/category.model';
import { SubCategory } from '../../modules/SubCategory/subCategory.model';
import { User } from '../../modules/User/user.model';
import { Teacher } from '../../modules/Teacher/teacher.model';
import config from '../../config';
import bcrypt from 'bcrypt';

export interface SeedingOptions {
  seedCategories: boolean;
  seedSubCategories: boolean;
  seedDefaultUsers: boolean;
  seedSampleData: boolean;
  overwriteExisting: boolean;
}

export interface SeedingResult {
  success: boolean;
  seededCounts: {
    categories: number;
    subCategories: number;
    users: number;
    teachers: number;
  };
  errors: string[];
  duration: number;
}

export class DatabaseSeedingService {
  private readonly defaultOptions: SeedingOptions = {
    seedCategories: true,
    seedSubCategories: true,
    seedDefaultUsers: true,
    seedSampleData: false,
    overwriteExisting: false
  };

  /**
   * Perform database seeding with default values
   */
  async performSeeding(options: Partial<SeedingOptions> = {}): Promise<SeedingResult> {
    const startTime = Date.now();
    const opts = { ...this.defaultOptions, ...options };
    
    Logger.info('🌱 Starting database seeding...', { options: opts });

    const result: SeedingResult = {
      success: false,
      seededCounts: { categories: 0, subCategories: 0, users: 0, teachers: 0 },
      errors: [],
      duration: 0
    };

    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        // Seed in dependency order
        if (opts.seedCategories) {
          result.seededCounts.categories = await this.seedCategories(opts, session);
        }
        
        if (opts.seedSubCategories) {
          result.seededCounts.subCategories = await this.seedSubCategories(opts, session);
        }
        
        if (opts.seedDefaultUsers) {
          const userCounts = await this.seedDefaultUsers(opts, session);
          result.seededCounts.users = userCounts.users;
          result.seededCounts.teachers = userCounts.teachers;
        }
      });

      result.success = true;
      result.duration = Date.now() - startTime;
      
      Logger.info('🎉 Database seeding completed successfully', {
        duration: result.duration,
        seededCounts: result.seededCounts
      });

    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
      result.duration = Date.now() - startTime;
      
      Logger.error('❌ Database seeding failed:', error);
      throw error;
    } finally {
      await session.endSession();
    }

    return result;
  }

  /**
   * Seed default categories
   */
  private async seedCategories(options: SeedingOptions, session: mongoose.ClientSession): Promise<number> {
    const defaultCategories = [
      {
        name: 'Programming & Development',
        slug: 'programming-development',
        description: 'Learn programming languages, frameworks, and development tools',
        icon: '💻',
        isActive: true
      },
      {
        name: 'Business & Entrepreneurship',
        slug: 'business-entrepreneurship',
        description: 'Business skills, entrepreneurship, and management',
        icon: '💼',
        isActive: true
      },
      {
        name: 'Design & Creative Arts',
        slug: 'design-creative-arts',
        description: 'Graphic design, UI/UX, and creative skills',
        icon: '🎨',
        isActive: true
      },
      {
        name: 'Data Science & Analytics',
        slug: 'data-science-analytics',
        description: 'Data analysis, machine learning, and statistics',
        icon: '📊',
        isActive: true
      },
      {
        name: 'Marketing & Sales',
        slug: 'marketing-sales',
        description: 'Digital marketing, sales strategies, and customer acquisition',
        icon: '📈',
        isActive: true
      },
      {
        name: 'Personal Development',
        slug: 'personal-development',
        description: 'Self-improvement, productivity, and life skills',
        icon: '🌟',
        isActive: true
      }
    ];

    let seededCount = 0;

    for (const categoryData of defaultCategories) {
      const existingCategory = await Category.findOne({ slug: categoryData.slug }).session(session);
      
      if (!existingCategory || options.overwriteExisting) {
        if (existingCategory && options.overwriteExisting) {
          await Category.findByIdAndUpdate(existingCategory._id, categoryData).session(session);
        } else {
          await Category.create([categoryData], { session });
        }
        seededCount++;
      }
    }

    Logger.info(`🌱 Categories seeding: ${seededCount} categories seeded`);
    return seededCount;
  }

  /**
   * Seed default subcategories
   */
  private async seedSubCategories(options: SeedingOptions, session: mongoose.ClientSession): Promise<number> {
    // Get categories first
    const categories = await Category.find({ isActive: true }).session(session);
    const categoryMap = new Map(categories.map(cat => [cat.slug, cat._id]));

    const defaultSubCategories = [
      // Programming & Development
      { categorySlug: 'programming-development', name: 'Web Development', slug: 'web-development', description: 'Frontend and backend web development' },
      { categorySlug: 'programming-development', name: 'Mobile Development', slug: 'mobile-development', description: 'iOS, Android, and cross-platform mobile apps' },
      { categorySlug: 'programming-development', name: 'Game Development', slug: 'game-development', description: 'Video game programming and design' },
      { categorySlug: 'programming-development', name: 'DevOps & Cloud', slug: 'devops-cloud', description: 'Cloud computing, CI/CD, and infrastructure' },
      
      // Business & Entrepreneurship
      { categorySlug: 'business-entrepreneurship', name: 'Startup Fundamentals', slug: 'startup-fundamentals', description: 'Starting and growing a business' },
      { categorySlug: 'business-entrepreneurship', name: 'Project Management', slug: 'project-management', description: 'Agile, Scrum, and project leadership' },
      { categorySlug: 'business-entrepreneurship', name: 'Finance & Accounting', slug: 'finance-accounting', description: 'Business finance and accounting principles' },
      
      // Design & Creative Arts
      { categorySlug: 'design-creative-arts', name: 'UI/UX Design', slug: 'ui-ux-design', description: 'User interface and experience design' },
      { categorySlug: 'design-creative-arts', name: 'Graphic Design', slug: 'graphic-design', description: 'Visual design and branding' },
      { categorySlug: 'design-creative-arts', name: 'Video Production', slug: 'video-production', description: 'Video editing and production' },
      
      // Data Science & Analytics
      { categorySlug: 'data-science-analytics', name: 'Machine Learning', slug: 'machine-learning', description: 'AI and machine learning algorithms' },
      { categorySlug: 'data-science-analytics', name: 'Data Visualization', slug: 'data-visualization', description: 'Charts, graphs, and data presentation' },
      { categorySlug: 'data-science-analytics', name: 'Business Intelligence', slug: 'business-intelligence', description: 'BI tools and analytics' },
      
      // Marketing & Sales
      { categorySlug: 'marketing-sales', name: 'Digital Marketing', slug: 'digital-marketing', description: 'Online marketing strategies' },
      { categorySlug: 'marketing-sales', name: 'Social Media Marketing', slug: 'social-media-marketing', description: 'Social platform marketing' },
      { categorySlug: 'marketing-sales', name: 'Content Marketing', slug: 'content-marketing', description: 'Content strategy and creation' },
      
      // Personal Development
      { categorySlug: 'personal-development', name: 'Leadership Skills', slug: 'leadership-skills', description: 'Leadership and management skills' },
      { categorySlug: 'personal-development', name: 'Communication', slug: 'communication', description: 'Public speaking and communication' },
      { categorySlug: 'personal-development', name: 'Productivity', slug: 'productivity', description: 'Time management and productivity' }
    ];

    let seededCount = 0;

    for (const subCatData of defaultSubCategories) {
      const categoryId = categoryMap.get(subCatData.categorySlug);
      if (!categoryId) {
        Logger.warn(`Category not found for slug: ${subCatData.categorySlug}`);
        continue;
      }

      const existingSubCategory = await SubCategory.findOne({ 
        categoryId, 
        slug: subCatData.slug 
      }).session(session);
      
      if (!existingSubCategory || options.overwriteExisting) {
        const subCategoryData = {
          categoryId,
          name: subCatData.name,
          slug: subCatData.slug,
          description: subCatData.description,
          isActive: true
        };

        if (existingSubCategory && options.overwriteExisting) {
          await SubCategory.findByIdAndUpdate(existingSubCategory._id, subCategoryData).session(session);
        } else {
          await SubCategory.create([subCategoryData], { session });
        }
        seededCount++;
      }
    }

    Logger.info(`🌱 SubCategories seeding: ${seededCount} subcategories seeded`);
    return seededCount;
  }

  /**
   * Seed default users and admin accounts
   */
  private async seedDefaultUsers(
    options: SeedingOptions, 
    session: mongoose.ClientSession
  ): Promise<{ users: number; teachers: number }> {
    let userCount = 0;
    let teacherCount = 0;

    // Create super admin if not exists
    const superAdminEmail = '<EMAIL>';
    const existingSuperAdmin = await User.findOne({ email: superAdminEmail }).session(session);
    
    if (!existingSuperAdmin || options.overwriteExisting) {
      const hashedPassword = await bcrypt.hash(config.super_admin_password || 'Admin123!@#', 12);
      
      const superAdminData = {
        email: superAdminEmail,
        password: hashedPassword,
        role: 'user', // Using 'user' as per the existing pattern
        status: 'in-progress',
        isDeleted: false,
        isVerified: true,
        photoUrl: ''
      };

      if (existingSuperAdmin && options.overwriteExisting) {
        await User.findByIdAndUpdate(existingSuperAdmin._id, superAdminData).session(session);
      } else {
        await User.create([superAdminData], { session });
      }
      userCount++;
    }

    // Create demo teacher account
    const demoTeacherEmail = '<EMAIL>';
    const existingDemoTeacher = await User.findOne({ email: demoTeacherEmail }).session(session);
    
    if (!existingDemoTeacher || options.overwriteExisting) {
      const hashedPassword = await bcrypt.hash('Teacher123!@#', 12);
      
      const demoTeacherUserData = {
        email: demoTeacherEmail,
        password: hashedPassword,
        role: 'teacher',
        status: 'in-progress',
        isDeleted: false,
        isVerified: true,
        photoUrl: ''
      };

      let teacherUserId;
      if (existingDemoTeacher && options.overwriteExisting) {
        await User.findByIdAndUpdate(existingDemoTeacher._id, demoTeacherUserData).session(session);
        teacherUserId = existingDemoTeacher._id;
      } else {
        const [createdUser] = await User.create([demoTeacherUserData], { session });
        teacherUserId = createdUser._id;
      }
      userCount++;

      // Create teacher profile
      const existingTeacherProfile = await Teacher.findOne({ email: demoTeacherEmail }).session(session);
      
      if (!existingTeacherProfile || options.overwriteExisting) {
        const teacherProfileData = {
          user: teacherUserId,
          name: {
            firstName: 'Demo',
            middleName: '',
            lastName: 'Teacher'
          },
          gender: 'other',
          email: demoTeacherEmail,
          profileImg: '',
          isDeleted: false,
          stripeConnect: {},
          stripeAuditLog: [],
          earnings: { total: 0, monthly: 0, yearly: 0, weekly: 0 },
          totalEarnings: 0,
          payments: [],
          courses: [],
          averageRating: 0
        };

        if (existingTeacherProfile && options.overwriteExisting) {
          await Teacher.findByIdAndUpdate(existingTeacherProfile._id, teacherProfileData).session(session);
        } else {
          await Teacher.create([teacherProfileData], { session });
        }
        teacherCount++;
      }
    }

    Logger.info(`🌱 Users seeding: ${userCount} users, ${teacherCount} teachers seeded`);
    return { users: userCount, teachers: teacherCount };
  }

  /**
   * Reset all models to default empty state
   */
  async resetToDefaults(): Promise<SeedingResult> {
    Logger.info('🔄 Resetting database to default state...');
    
    // First perform cleanup
    const { databaseCleanupService } = await import('./DatabaseCleanupService');
    await databaseCleanupService.performCleanup({
      preserveUsers: false, // We'll recreate default users
      preserveTeachers: false, // We'll recreate default teacher
      preserveCategories: false, // We'll recreate default categories
      preserveSubCategories: false, // We'll recreate default subcategories
      dryRun: false
    });

    // Then perform seeding
    return await this.performSeeding({
      seedCategories: true,
      seedSubCategories: true,
      seedDefaultUsers: true,
      seedSampleData: false,
      overwriteExisting: true
    });
  }

  /**
   * Verify seeded data integrity
   */
  async verifySeeding(): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = [];

    try {
      // Check essential data exists
      const [categoryCount, subCategoryCount, userCount, teacherCount] = await Promise.all([
        Category.countDocuments({ isActive: true }),
        SubCategory.countDocuments({ isActive: true }),
        User.countDocuments({ isDeleted: { $ne: true } }),
        Teacher.countDocuments({ isDeleted: { $ne: true } })
      ]);

      if (categoryCount === 0) {
        issues.push('No active categories found after seeding');
      }

      if (subCategoryCount === 0) {
        issues.push('No active subcategories found after seeding');
      }

      if (userCount === 0) {
        issues.push('No users found after seeding');
      }

      // Check admin user exists
      const adminUser = await User.findOne({ email: '<EMAIL>' });
      if (!adminUser) {
        issues.push('Super admin user not found after seeding');
      }

      // Check category-subcategory relationships
      const orphanedSubCategories = await SubCategory.countDocuments({
        categoryId: { $nin: await Category.find({ isActive: true }).distinct('_id') }
      });

      if (orphanedSubCategories > 0) {
        issues.push(`Found ${orphanedSubCategories} orphaned subcategories`);
      }

      Logger.info('🔍 Seeding verification completed', {
        counts: { categoryCount, subCategoryCount, userCount, teacherCount },
        issuesFound: issues.length
      });

      return {
        valid: issues.length === 0,
        issues
      };

    } catch (error) {
      issues.push(`Seeding verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { valid: false, issues };
    }
  }
}

// Export singleton instance
export const databaseSeedingService = new DatabaseSeedingService();
